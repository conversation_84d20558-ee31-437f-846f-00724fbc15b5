import { CreatePageDto } from '../../src/modules/editor/application/services/page.dto';
import { PageService } from '../../src/modules/editor/application/services/page.service';
import { createGqlTestSession, GqlTestSession } from '../user-session';
import { PLANS_USER } from './test-user';
import { HocuspocusTestClient } from './utils/hocuspocus-client';
import { TestServer } from './utils/test-server';

describe('Tiptap Editor Integration Tests', () => {
    let testServer: TestServer;
    let userSession: GqlTestSession;
    let pageService: PageService;
    let baseUrl: string;

    const PAGE_ID = '019759c6-35c8-7799-8f21-2be79530d07d';

    beforeAll(async () => {
        testServer = new TestServer();
        const { port } = await testServer.start();
        baseUrl = `http://localhost:${port}`;

        userSession = await createGqlTestSession(testServer.getApp(), PLANS_USER);
        pageService = testServer.getApp().get<PageService>(PageService);
    });

    afterAll(async () => {
        if (testServer) {
            await testServer.stop();
        }
    });

    describe('Tiptap Editor Functionality', () => {
        it('должен создать документ, внести изменения через редактор и проверить сериализацию', async () => {
            // 1. Создаем документ через pageService
            const pageDto: CreatePageDto = {
                serviceId: 'tiptap-test-service',
                entityId: 'tiptap-test-entity',
                schoolId: 'tiptap-test-school',
            };

            const page = await pageService.create(pageDto);
            expect(page).toBeDefined();

            // 2. Подключаемся к документу через WebSocket
            const client = new HocuspocusTestClient({
                url: baseUrl,
                documentName: page.id,
            });

            await client.connect();
            expect(client.isConnected()).toBe(true);

            // 3. Ждем синхронизации
            await client.waitForSync(1000);

            // 4. Проверяем начальное состояние документа
            let content = client.getContent();
            expect(content).toBeDefined();
            expect(content.type).toBe('doc');

            // 5. Вносим изменения с использованием Tiptap extensions

            // Очищаем содержимое и начинаем заново
            client.clearContent();

            // Добавляем заголовок
            client.insertText('Заголовок документа');
            client.setHeading(1);

            // Добавляем параграф с форматированием
            client.insertParagraph();
            client.insertText('Обычный текст, ');
            client.insertBoldText('жирный текст');
            client.insertText(', ');
            client.insertItalicText('курсивный текст');
            client.insertText('.');

            // Добавляем bullet list
            client.insertBulletList(['Первый элемент списка', 'Второй элемент списка', 'Третий элемент списка']);

            // Добавляем ordered list
            client.insertOrderedList(['Первый пункт', 'Второй пункт', 'Третий пункт']);

            // Добавляем ссылку
            client.insertParagraph();
            client.insertLink('https://example.com', 'Ссылка на пример');

            // Добавляем blockquote
            client.insertParagraph();
            client.insertText('Это цитата');
            client.insertBlockquote();

            // Добавляем code block
            client.insertCodeBlock('javascript');
            client.insertText('console.log("Hello, World!");');

            // 6. Проверяем содержимое клиента
            content = client.getContent();
            expect(content.type).toBe('doc');
            expect(content.content).toBeDefined();
            expect(Array.isArray(content.content)).toBe(true);

            // Проверяем HTML содержимое
            const htmlContent = client.getHTML();
            expect(htmlContent).toContain('<h1>');
            expect(htmlContent).toContain('<strong>');
            expect(htmlContent).toContain('<em>');
            expect(htmlContent).toContain('<ul>');
            expect(htmlContent).toContain('<ol>');
            expect(htmlContent).toContain('<a href="https://example.com">');

            // Проверяем текстовое содержимое
            const textContent = client.getText();
            expect(textContent).toContain('Заголовок документа');
            expect(textContent).toContain('жирный текст');
            expect(textContent).toContain('курсивный текст');
            expect(textContent).toContain('Первый элемент списка');
            expect(textContent).toContain('Ссылка на пример');

            // 7. Ждем сохранения изменений
            await client.waitForSync(2000);

            // 8. Отключаемся от WebSocket
            client.disconnect();

            // 9. Получаем документ через GraphQL и проверяем изменения
            const updatedPage = await userSession.getPage(page.id);
            expect(updatedPage).toBeDefined();
            expect(updatedPage.id).toBe(page.id);
            expect(updatedPage.serviceId).toBe('tiptap-test-service');
            expect(updatedPage.entityId).toBe('tiptap-test-entity');
            expect(updatedPage.schoolId).toBe('tiptap-test-school');
        });

        it('должен обработать множественные подключения к одному документу', async () => {
            // Создаем документ
            const pageDto: CreatePageDto = {
                serviceId: 'multi-client-service',
                entityId: 'multi-client-entity',
                schoolId: 'multi-client-school',
            };

            const page = await pageService.create(pageDto);

            // Подключаем два клиента
            const client1 = new HocuspocusTestClient({
                url: baseUrl,
                documentName: page.id,
            });

            const client2 = new HocuspocusTestClient({
                url: baseUrl,
                documentName: page.id,
            });

            await Promise.all([client1.connect(), client2.connect()]);

            expect(client1.isConnected()).toBe(true);
            expect(client2.isConnected()).toBe(true);

            // Ждем синхронизации
            await Promise.all([client1.waitForSync(1000), client2.waitForSync(1000)]);

            // Первый клиент добавляет текст
            client1.insertText('Текст от клиента 1');
            await client1.waitForSync(1000);

            // Второй клиент добавляет текст
            client2.insertText('Текст от клиента 2');
            await client2.waitForSync(1000);

            // Отключаем клиентов
            client1.disconnect();
            client2.disconnect();

            // Проверяем результат через GraphQL
            const finalPage = await userSession.getPage(page.id);
            expect(finalPage).toBeDefined();
            expect(finalPage.id).toBe(page.id);
        });

        it('должен корректно обработать отключение от несуществующего документа', async () => {
            const nonExistentId = '01234567-89ab-cdef-0123-456789abcdef';

            const client = new HocuspocusTestClient({
                url: baseUrl,
                documentName: nonExistentId,
            });

            // Подключение должно пройти успешно, но документ будет пустым
            await expect(client.connect()).resolves.not.toThrow();
            expect(client.isConnected()).toBe(true);

            // Документ должен быть пустым
            const content = client.getContent();
            expect(content.type).toBe('doc');

            client.disconnect();

            // Проверяем, что страница НЕ была создана в базе данных
            const errorMessage = await userSession.expectGraphQLError(async () => {
                await userSession.getPage(nonExistentId);
            });

            expect(errorMessage).toContain('not found');
        });

        it('должен правильно сериализовать различные типы форматирования текста', async () => {
            const pageDto: CreatePageDto = {
                serviceId: 'formatting-test-service',
                entityId: 'formatting-test-entity',
                schoolId: 'formatting-test-school',
            };

            const page = await pageService.create(pageDto);
            const client = new HocuspocusTestClient({
                url: baseUrl,
                documentName: page.id,
            });

            await client.connect();
            await client.waitForSync(1000);

            // Очищаем содержимое
            client.clearContent();

            // Тестируем различные типы форматирования
            client.insertText('Обычный текст ');
            client.toggleBold();
            client.insertText('жирный ');
            client.toggleBold();
            client.toggleItalic();
            client.insertText('курсив ');
            client.toggleItalic();
            client.toggleUnderline();
            client.insertText('подчеркнутый ');
            client.toggleUnderline();
            client.toggleStrike();
            client.insertText('зачеркнутый ');
            client.toggleStrike();
            client.toggleSuperscript();
            client.insertText('верхний индекс ');
            client.toggleSuperscript();
            client.toggleSubscript();
            client.insertText('нижний индекс');
            client.toggleSubscript();

            // Проверяем JSON содержимое
            const content = client.getContent();
            expect(content.type).toBe('doc');
            expect(content.content).toBeDefined();

            // Проверяем HTML содержимое
            const htmlContent = client.getHTML();
            expect(htmlContent).toContain('<strong>жирный</strong>');
            expect(htmlContent).toContain('<em>курсив</em>');
            expect(htmlContent).toContain('<u>подчеркнутый</u>');
            expect(htmlContent).toContain('<s>зачеркнутый</s>');
            expect(htmlContent).toContain('<sup>верхний индекс</sup>');
            expect(htmlContent).toContain('<sub>нижний индекс</sub>');

            client.disconnect();
        });

        it.only('должен правильно сериализовать таблицы и изображения', async () => {
            const pageDto: CreatePageDto = {
                serviceId: 'media-test-service',
                entityId: 'media-test-entity',
                schoolId: 'media-test-school',
            };

            const page = await pageService.create(pageDto);
            const client = new HocuspocusTestClient({
                url: baseUrl,
                documentName: page.id,
            });

            await client.connect();
            await client.waitForSync(1000);

            client.clearContent();

            // Добавляем заголовок
            client.insertText('Тестирование медиа элементов');
            client.setHeading(2);

            // Добавляем изображение
            client.insertParagraph();
            client.insertImage('https://example.com/image.jpg', 'Тестовое изображение');

            // Добавляем таблицу
            client.insertParagraph();
            client.insertTable(2, 3);

            // Добавляем YouTube видео
            client.insertParagraph();
            client.insertYouTube('https://www.youtube.com/watch?v=dQw4w9WgXcQ');

            // Добавляем горизонтальную линию
            client.insertParagraph();
            client.insertHorizontalRule();

            // Проверяем JSON содержимое
            const content = client.getContent();
            console.log('JSON content:', content);
            expect(content.type).toBe('doc');
            expect(content.content).toBeDefined();

            // Проверяем HTML содержимое
            const htmlContent = client.getHTML();
            console.log('HTML content:', htmlContent);
            expect(htmlContent).toContain('<h2>');
            expect(htmlContent).toContain('<img');
            expect(htmlContent).toContain('src="https://example.com/image.jpg"');
            expect(htmlContent).toContain('<table>');
            expect(htmlContent).toContain('<hr>');

            client.disconnect();
        });

        it('должен правильно сериализовать задачи и выравнивание текста', async () => {
            const pageDto: CreatePageDto = {
                serviceId: 'tasks-test-service',
                entityId: 'tasks-test-entity',
                schoolId: 'tasks-test-school',
            };

            const page = await pageService.create(pageDto);
            const client = new HocuspocusTestClient({
                url: baseUrl,
                documentName: page.id,
            });

            await client.connect();
            await client.waitForSync(1000);

            client.clearContent();

            // Добавляем заголовок с выравниванием по центру
            client.insertText('Центрированный заголовок');
            client.setHeading(1);
            client.setTextAlign('center');

            // Добавляем список задач
            client.insertParagraph();
            client.insertTaskList([
                { text: 'Выполненная задача', checked: true },
                { text: 'Невыполненная задача', checked: false },
                { text: 'Еще одна задача', checked: false },
            ]);

            // Добавляем текст с выравниванием по правому краю
            client.insertParagraph();
            client.insertText('Текст справа');
            client.setTextAlign('right');

            // Добавляем цветной текст
            client.insertParagraph();
            client.setTextColor('#ff0000');
            client.insertText('Красный текст ');
            client.setHighlight('#ffff00');
            client.insertText('с желтым фоном');

            // Проверяем JSON содержимое
            const content = client.getContent();
            expect(content.type).toBe('doc');
            expect(content.content).toBeDefined();

            // Проверяем HTML содержимое
            const htmlContent = client.getHTML();
            expect(htmlContent).toContain('text-align: center');
            expect(htmlContent).toContain('text-align: right');
            expect(htmlContent).toContain('data-type="taskList"');
            expect(htmlContent).toContain('data-checked="true"');
            expect(htmlContent).toContain('data-checked="false"');

            // Проверяем текстовое содержимое
            const textContent = client.getText();
            expect(textContent).toContain('Центрированный заголовок');
            expect(textContent).toContain('Выполненная задача');
            expect(textContent).toContain('Невыполненная задача');
            expect(textContent).toContain('Красный текст');

            client.disconnect();
        });
    });
});
